<template>
  <BasicDrawer
    v-bind="$attrs"
    title="检修项详情-发动机日常检查"
    :width="1200"
    :footer="false"
    @register="registerInnerDrawer"
  >
    <div v-loading="loading" class="detail-container">
      <!-- 上半部分：基本信息和图表 -->
      <Row :gutter="24" class="mb-6">
        <!-- 左侧：基本信息 -->
        <Col :span="12">
          <div class="info-section">
            <div class="section-title">
              <Icon icon="ant-design:info-circle-outlined" class="mr-2" />
              基本信息
            </div>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">检修项编号</span>
                <span class="value">{{ detailData?.itemNum || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">检修项名称</span>
                <span class="value">{{ detailData?.itemName || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">适用车型</span>
                <span class="value">{{ detailData?.vehicleType || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">部件类型</span>
                <span class="value">{{ detailData?.componentType || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">检修频率</span>
                <span class="value">每日</span>
              </div>
              <div class="info-item">
                <span class="label">检修点数</span>
                <span class="value">{{ detailData?.pointCount || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="label">重点检修点数</span>
                <span class="value">{{ keyPointCount }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建人</span>
                <span class="value">{{ detailData?.createUser || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间</span>
                <span class="value">{{ detailData?.createTime || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态</span>
                <span class="value">
                  <Tag color="green">{{ getStatusText(detailData?.status) }}</Tag>
                </span>
              </div>
            </div>
          </div>

          <!-- 检修标准流程 -->
          <div class="info-section mt-6">
            <div class="section-title">
              <Icon icon="ant-design:ordered-list-outlined" class="mr-2" />
              检修标准流程
            </div>
            <div class="process-list">
              <div v-for="(step, index) in processSteps" :key="index" class="process-item">
                <span class="step-number">{{ index + 1 }}.</span>
                <span class="step-content">{{ step }}</span>
              </div>
            </div>
          </div>

          <!-- 示意图 -->
          <div class="info-section mt-6">
            <div class="section-title">
              <Icon icon="ant-design:picture-outlined" class="mr-2" />
              示意图
            </div>
            <div class="image-container">
              <Image
                v-if="detailData?.itemImg"
                :src="detailData.itemImg"
                alt="检修项示意图"
                :height="200"
                class="w-full object-cover"
              />
              <div v-else class="no-image">
                <Icon icon="ant-design:picture-outlined" class="text-4xl text-gray-400" />
                <span class="text-gray-500">暂无示意图</span>
              </div>
            </div>
          </div>
        </Col>

        <!-- 右侧：检修分布图表 -->
        <Col :span="12">
          <div class="info-section">
            <div class="section-title">
              <Icon icon="ant-design:pie-chart-outlined" class="mr-2" />
              检修分布
            </div>
            <div class="chart-container">
              <div ref="chartRef" class="chart" style="height: 300px;"></div>
              <div class="chart-legend">
                <div class="legend-item">
                  <div class="legend-color" style="background-color: #ff4d4f;"></div>
                  <span>重点检修点</span>
                  <span class="legend-value">{{ keyPointCount }}</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color" style="background-color: #8c8c8c;"></div>
                  <span>非重点检修点</span>
                  <span class="legend-value">{{ normalPointCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </Col>
      </Row>

      <!-- 下半部分：检修点列表 -->
      <div class="table-section">
        <div class="section-title mb-4">
          <Icon icon="ant-design:table-outlined" class="mr-2" />
          检修点列表
          <Button type="primary" size="small" class="ml-4" @click="handleAddPoint">
            <Icon icon="ant-design:plus-outlined" class="mr-1" />
            新增
          </Button>
        </div>
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'isKeyPoint'">
              <Tag :color="record.isKeyPoint === 1 ? 'red' : 'default'">
                {{ record.isKeyPoint === 1 ? '重点' : '非重点' }}
              </Tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: '查看',
                    icon: 'ant-design:eye-outlined',
                    onClick: handleViewPoint.bind(null, record),
                  },
                  {
                    label: '编辑',
                    icon: 'ant-design:edit-outlined',
                    onClick: handleEditPoint.bind(null, record),
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <!-- 检修点相关弹窗 -->
    <!-- <InspectionPointModal @register="registerPointModal" @reload="reloadTable" />
    <InspectionPointInfoModal @register="registerPointInfoModal" /> -->
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick } from 'vue';
  import { Row, Col, Tag, Button, Image } from 'ant-design-vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { inspectionItemDetail } from '@/api/security/vehicleInspectionItem';
  import { inspectionPointList } from '@/api/security/vehicleInspectionPoint';
  // import InspectionPointModal from './CheckpointModal/Modal.vue';
  // import InspectionPointInfoModal from './CheckpointModal/InfoModal.vue';

  defineOptions({ name: 'InspectionItemDetailDrawer' });

  const loading = ref(false);
  const detailData = ref<any>(null);
  const pointsData = ref<any[]>([]);
  const chartRef = ref<HTMLDivElement>() as any;
  const currentInspectionItemId = ref<string | number>('');

  // 计算属性
  const keyPointCount = computed(() => {
    return pointsData.value.filter((item: any) => item.isKeyPoint === 1).length;
  });

  const normalPointCount = computed(() => {
    return pointsData.value.filter((item: any) => item.isKeyPoint === 0).length;
  });

  const processSteps = computed(() => {
    if (!detailData.value?.standardProcess) return [];
    // 假设标准流程是用换行符分隔的步骤
    return detailData.value.standardProcess.split('\n').filter((step: string) => step.trim());
  });

  // 状态文本映射
  const getStatusText = (status: string) => {
    const statusMap = {
      '1': '启用',
      '0': '停用',
      'active': '启用',
      'inactive': '停用'
    };
    return statusMap[status] || '未知';
  };

  // ECharts 图表
  const { setOptions: setChartOptions } = useECharts(chartRef);

  // 更新图表
  const updateChart = () => {
    if (!chartRef.value) return;
    
    const option: any = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '检修点分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            {
              value: keyPointCount.value,
              name: '重点检修点',
              itemStyle: { color: '#ff4d4f' }
            },
            {
              value: normalPointCount.value,
              name: '非重点检修点',
              itemStyle: { color: '#8c8c8c' }
            }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: false
          },
          labelLine: {
            show: false
          }
        }
      ]
    };
    
    setChartOptions(option);
  };

  // 表格配置
  const columns: any[] = [
    { title: '序号', dataIndex: 'orderNum', width: 80 },
    { title: '检修点名称', dataIndex: 'pointName', width: 200 },
    { title: '位置描述', dataIndex: 'locationDesc', width: 300 },
    { title: '重点标识', dataIndex: 'isKeyPoint', key: 'isKeyPoint', width: 100 },
    { title: '操作', key: 'action', width: 150, fixed: 'right' }
  ];

  const [registerTable, { reload: reloadTable }] = useTable({
    dataSource: pointsData,
    columns,
    showIndexColumn: false,
    pagination: false,
    actionColumn: {
      width: 150,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  // Modal 注册
  // const [registerPointModal, { openModal: openPointModal }] = useModal();
  // const [registerPointInfoModal, { openModal: openPointInfoModal }] = useModal();

  // 事件处理
  const handleAddPoint = () => {
    console.log('添加检修点');
    // openPointModal(true, {
    //   update: false,
    //   inspectionItemId: currentInspectionItemId.value
    // });
  };

  const handleViewPoint = (record: any) => {
    console.log('查看检修点', record);
    // openPointInfoModal(true, record.id);
  };

  const handleEditPoint = (record: any) => {
    console.log('编辑检修点', record);
    // openPointModal(true, {
    //   record,
    //   update: true,
    //   inspectionItemId: currentInspectionItemId.value
    // });
  };

  // Drawer 注册
  const [registerInnerDrawer] = useDrawerInner(async (inspectionItemId: string | number) => {
    if (!inspectionItemId) return;
    
    loading.value = true;
    currentInspectionItemId.value = inspectionItemId;
    
    try {
      // 并行获取详情和检修点数据
      const [detailResponse, pointsResponse] = await Promise.all([
        inspectionItemDetail(inspectionItemId),
        inspectionPointList({ id: inspectionItemId } as any)
      ]);

      detailData.value = detailResponse;
      pointsData.value = Array.isArray(pointsResponse) ? pointsResponse : ((pointsResponse as any)?.records || []);
      
      // 更新图表
      nextTick(() => {
        updateChart();
      });
      
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  });
</script>

<style scoped lang="less">
.detail-container {
  padding: 16px;

  .info-section {
    padding: 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
      color: #1890ff;
      font-size: 16px;
      font-weight: 600;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          width: 120px;
          color: #666;
          font-size: 14px;
        }

        .value {
          flex: 1;
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .process-list {
      .process-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .step-number {
          flex-shrink: 0;
          width: 24px;
          color: #1890ff;
          font-weight: 600;
        }

        .step-content {
          flex: 1;
          color: #333;
          line-height: 1.5;
        }
      }
    }

    .image-container {
      .no-image {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        background: #fafafa;
      }
    }

    .chart-container {
      .chart {
        margin-bottom: 16px;
      }

      .chart-legend {
        display: flex;
        justify-content: center;
        gap: 24px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
          }

          .legend-value {
            color: #333;
            font-weight: 600;
          }
        }
      }
    }
  }

  .table-section {
    padding: 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);

    .section-title {
      display: flex;
      align-items: center;
      color: #1890ff;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
