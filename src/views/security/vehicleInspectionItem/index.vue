<template>
  <PageWrapper dense>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button @click="handleAdd" type="primary">新增</a-button>
          <a-button
            type="primary"
            danger
            @click="multipleRemove(inspectionItemRemove)"
            :disabled="!selected"
          >
            删除
          </a-button>
          <a-button
            @click="downloadExcel(inspectionItemExport, '检修项列表', getForm().getFieldsValue())"
          >
            导出
          </a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '检查点',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleCheckpoint.bind(null, record),
              },
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '编辑',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <InspectionItemModal @register="registerModal" @reload="reload" />
    <InspectionItemInfoModal @register="registerInfoModal" />
    <InspectionItemDetailDrawer @register="registerDetailDrawer" />
    <CheckpointModal width="80%" :footer="false" @register="registerCheckpointModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    inspectionItemList,
    inspectionItemExport,
    inspectionItemRemove,
  } from '@/api/security/vehicleInspectionItem';
  import InspectionItemModal from './Modal.vue';
  import InspectionItemInfoModal from './InfoModal.vue';
  import InspectionItemDetailDrawer from './DetailDrawer.vue';
  import CheckpointModal from './CheckpointModal/index.vue';
  import { useModal } from '@/components/Modal';
  import { useDrawer } from '@/components/Drawer';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'InspectionItem' });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    indexColumnProps: {
      width: 500,
    },
    rowSelection: {
      type: 'checkbox',
    },
    title: '检修项管理',
    showIndexColumn: false,
    api: inspectionItemList,
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'inspectionItem',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 300,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await inspectionItemRemove([id]);
    await reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();
  const [registerDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();

  function handleInfo(record: Recordable) {
    const { id } = record;
    // 使用新的 Drawer 组件显示详情
    openDetailDrawer(true, id);
  }

  const [registerCheckpointModal, { openModal: openCheckpointModal }] = useModal();

  function handleCheckpoint(record: Recordable) {
    const { id } = record;
    openCheckpointModal(true, id);
  }
</script>

<style scoped></style>
