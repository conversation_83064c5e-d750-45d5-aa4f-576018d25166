<template>
  <BasicDrawer
    v-bind="$attrs"
    title="检修项详情"
    :width="1200"
    :footer="false"
    @register="registerInnerDrawer"
  >
    <div v-loading="loading" class="detail-container">
      <div class="p-4">
        <h3>基本信息</h3>
        <div v-if="detailData">
          <p><strong>检修项编号:</strong> {{ detailData.itemNum }}</p>
          <p><strong>检修项名称:</strong> {{ detailData.itemName }}</p>
          <p><strong>适用车型:</strong> {{ detailData.vehicleType }}</p>
          <p><strong>部件类型:</strong> {{ detailData.componentType }}</p>
          <p><strong>状态:</strong> {{ detailData.status }}</p>
        </div>
        <div v-else>
          <p>加载中...</p>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { inspectionItemDetail } from '@/api/security/vehicleInspectionItem';

  defineOptions({ name: 'InspectionItemDetailDrawerSimple' });

  const loading = ref(false);
  const detailData = ref<any>(null);

  // Drawer 注册
  const [registerInnerDrawer] = useDrawerInner(async (inspectionItemId: string | number) => {
    if (!inspectionItemId) return;
    
    loading.value = true;
    
    try {
      const response = await inspectionItemDetail(inspectionItemId);
      detailData.value = response;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  });
</script>

<style scoped>
.detail-container {
  padding: 16px;
}
</style>
